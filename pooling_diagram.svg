<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .grid-cell {
        fill: #f0f0f0;
        stroke: #333;
        stroke-width: 1;
      }
      .result-cell-max {
        fill: #ffcccc;
        stroke: #333;
        stroke-width: 1;
      }
      .result-cell-avg {
        fill: #ccffcc;
        stroke: #333;
        stroke-width: 1;
      }
      .result-cell-global {
        fill: #ffffcc;
        stroke: #333;
        stroke-width: 1;
      }
      .text {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        font-size: 14px;
        text-anchor: middle;
        dominant-baseline: middle;
      }
      .label-text {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        font-size: 12px;
        fill: #333;
      }
      .arrow {
        stroke: #d32f2f;
        stroke-width: 2;
        fill: none;
        marker-end: url(#arrowhead);
      }
      .arrow-text {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        font-size: 12px;
        fill: #d32f2f;
        text-anchor: middle;
      }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#d32f2f" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="400" y="30" class="text" style="font-size: 18px; font-weight: bold;">池化操作示意图</text>
  
  <!-- 输入矩阵标签 -->
  <text x="50" y="70" class="label-text">核尺寸：2×2  步长：2</text>
  
  <!-- 输入矩阵 4x4 -->
  <g transform="translate(50, 90)">
    <!-- 第一行 -->
    <rect x="0" y="0" width="40" height="40" class="grid-cell"/>
    <text x="20" y="20" class="text">8</text>
    <rect x="40" y="0" width="40" height="40" class="grid-cell"/>
    <text x="60" y="20" class="text">3</text>
    <rect x="80" y="0" width="40" height="40" class="grid-cell"/>
    <text x="100" y="20" class="text">5</text>
    <rect x="120" y="0" width="40" height="40" class="grid-cell"/>
    <text x="140" y="20" class="text">9</text>

    <!-- 第二行 -->
    <rect x="0" y="40" width="40" height="40" class="grid-cell"/>
    <text x="20" y="60" class="text">2</text>
    <rect x="40" y="40" width="40" height="40" class="grid-cell"/>
    <text x="60" y="60" class="text">7</text>
    <rect x="80" y="40" width="40" height="40" class="grid-cell"/>
    <text x="100" y="60" class="text">1</text>
    <rect x="120" y="40" width="40" height="40" class="grid-cell"/>
    <text x="140" y="60" class="text">6</text>

    <!-- 第三行 -->
    <rect x="0" y="80" width="40" height="40" class="grid-cell"/>
    <text x="20" y="100" class="text">4</text>
    <rect x="40" y="80" width="40" height="40" class="grid-cell"/>
    <text x="60" y="100" class="text">10</text>
    <rect x="80" y="80" width="40" height="40" class="grid-cell"/>
    <text x="100" y="100" class="text">3</text>
    <rect x="120" y="80" width="40" height="40" class="grid-cell"/>
    <text x="140" y="100" class="text">8</text>

    <!-- 第四行 -->
    <rect x="0" y="120" width="40" height="40" class="grid-cell"/>
    <text x="20" y="140" class="text">1</text>
    <rect x="40" y="120" width="40" height="40" class="grid-cell"/>
    <text x="60" y="140" class="text">5</text>
    <rect x="80" y="120" width="40" height="40" class="grid-cell"/>
    <text x="100" y="140" class="text">2</text>
    <rect x="120" y="120" width="40" height="40" class="grid-cell"/>
    <text x="140" y="140" class="text">7</text>
  </g>
  
  <!-- 最大池化箭头和标签 -->
  <path d="M 270 120 Q 320 120 370 120" class="arrow"/>
  <text x="320" y="110" class="arrow-text">最大池化</text>
  
  <!-- 最大池化结果 2x2 -->
  <g transform="translate(380, 90)">
    <rect x="0" y="0" width="40" height="40" class="result-cell-max"/>
    <text x="20" y="20" class="text">8</text>
    <rect x="40" y="0" width="40" height="40" class="result-cell-max"/>
    <text x="60" y="20" class="text">9</text>
    <rect x="0" y="40" width="40" height="40" class="result-cell-max"/>
    <text x="20" y="60" class="text">10</text>
    <rect x="40" y="40" width="40" height="40" class="result-cell-max"/>
    <text x="60" y="60" class="text">8</text>
  </g>
  
  <!-- 均值池化箭头和标签 -->
  <path d="M 270 160 Q 320 160 370 160" class="arrow"/>
  <text x="320" y="150" class="arrow-text">均值池化</text>
  
  <!-- 均值池化结果 2x2 -->
  <g transform="translate(380, 170)">
    <rect x="0" y="0" width="40" height="40" class="result-cell-avg"/>
    <text x="20" y="20" class="text">5</text>
    <rect x="40" y="0" width="40" height="40" class="result-cell-avg"/>
    <text x="60" y="20" class="text">5.25</text>
    <rect x="0" y="40" width="40" height="40" class="result-cell-avg"/>
    <text x="20" y="60" class="text">5</text>
    <rect x="40" y="40" width="40" height="40" class="result-cell-avg"/>
    <text x="60" y="60" class="text">5</text>
  </g>
  
  <!-- 全局均值池化箭头和标签 -->
  <path d="M 270 220 Q 320 220 370 260" class="arrow"/>
  <text x="320" y="210" class="arrow-text">全局均值池化</text>
  
  <!-- 全局均值池化结果 1x1 -->
  <g transform="translate(380, 250)">
    <rect x="0" y="0" width="60" height="40" class="result-cell-global"/>
    <text x="30" y="20" class="text">5.06</text>
  </g>
  
  <!-- 说明文字 -->
  <g transform="translate(500, 90)">
    <text x="0" y="20" class="label-text">最大池化：取每个2×2区域的最大值</text>
    <text x="0" y="40" class="label-text">左上：max(8,3,2,7) = 8</text>
    <text x="0" y="60" class="label-text">右上：max(5,9,1,6) = 9</text>
    <text x="0" y="80" class="label-text">左下：max(4,10,1,5) = 10</text>
    <text x="0" y="100" class="label-text">右下：max(3,8,2,7) = 8</text>

    <text x="0" y="140" class="label-text">均值池化：取每个2×2区域的平均值</text>
    <text x="0" y="160" class="label-text">左上：(8+3+2+7)/4 = 5</text>
    <text x="0" y="180" class="label-text">右上：(5+9+1+6)/4 = 5.25</text>
    <text x="0" y="200" class="label-text">左下：(4+10+1+5)/4 = 5</text>
    <text x="0" y="220" class="label-text">右下：(3+8+2+7)/4 = 5</text>

    <text x="0" y="260" class="label-text">全局均值池化：整个矩阵的平均值</text>
    <text x="0" y="280" class="label-text">(8+3+5+9+2+7+1+6+4+10+3+8+1+5+2+7)/16 = 5.06</text>
  </g>
  
  <!-- 池化窗口示意 -->
  <g transform="translate(50, 90)">
    <!-- 左上窗口 -->
    <rect x="-2" y="-2" width="84" height="84" fill="none" stroke="#ff6b6b" stroke-width="3" stroke-dasharray="5,5" opacity="0.7"/>
    <!-- 右上窗口 -->
    <rect x="78" y="-2" width="84" height="84" fill="none" stroke="#4ecdc4" stroke-width="3" stroke-dasharray="5,5" opacity="0.7"/>
    <!-- 左下窗口 -->
    <rect x="-2" y="78" width="84" height="84" fill="none" stroke="#45b7d1" stroke-width="3" stroke-dasharray="5,5" opacity="0.7"/>
    <!-- 右下窗口 -->
    <rect x="78" y="78" width="84" height="84" fill="none" stroke="#96ceb4" stroke-width="3" stroke-dasharray="5,5" opacity="0.7"/>
  </g>
</svg>
