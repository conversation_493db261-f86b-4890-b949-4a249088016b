% 激活函数可视化
% 生成Sigmoid、<PERSON><PERSON>、ReL<PERSON>、Leaky ReLU的图像

% 创建输入范围
x = -10:0.1:10;

% 创建图形窗口，调整大小以确保完整显示横轴-10到10
figure('Position', [100, 100, 1400, 350]);

% (a) Sigmoid函数
subplot(1, 4, 1);
y_sigmoid = 1 ./ (1 + exp(-x));
plot(x, y_sigmoid, 'r-', 'LineWidth', 2);
grid on;
xlim([-10, 10]);
ylim([-0.1, 1.1]);
xlabel('x');
ylabel('f(x)');
title('(a) Sigmoid');
set(gca, 'XTick', [-10, -5, 0, 5, 10]);
set(gca, 'YTick', [0, 0.5, 1]);
box on;

% (b) Tanh函数
subplot(1, 4, 2);
y_tanh = tanh(x);
plot(x, y_tanh, 'c-', 'LineWidth', 2);
grid on;
xlim([-10, 10]);
ylim([-1.1, 1.1]);
xlabel('x');
ylabel('f(x)');
title('(b) Tanh');
set(gca, 'XTick', [-10, -5, 0, 5, 10]);
set(gca, 'YTick', [-1, 0, 1]);
box on;

% (c) ReLU函数
subplot(1, 4, 3);
y_relu = max(0, x);
plot(x, y_relu, 'b-', 'LineWidth', 2);
grid on;
xlim([-10, 10]);
ylim([-1, 10]);
xlabel('x');
ylabel('f(x)');
title('(c) ReLU');
set(gca, 'XTick', [-10, -5, 0, 5, 10]);
set(gca, 'YTick', [0, 5, 10]);
box on;

% (d) Leaky ReLU函数
subplot(1, 4, 4);
alpha = 0.01; % Leaky ReLU的负斜率参数
y_leaky_relu = max(alpha * x, x);
plot(x, y_leaky_relu, 'm-', 'LineWidth', 2);
grid on;
xlim([-10, 10]);
ylim([-1, 10]);
xlabel('x');
ylabel('f(x)');
title('(d) Leaky ReLU');
set(gca, 'XTick', [-10, -5, 0, 5, 10]);
set(gca, 'YTick', [ 0, 5, 10]);
box on;

% 调整子图间距，确保完整显示
set(gcf, 'PaperPositionMode', 'auto');


% 保存图像
saveas(gcf, 'four_activation_functions.png', 'png');
saveas(gcf, 'four_activation_functions.fig', 'fig');

fprintf('四个激活函数图像已生成并保存\n');
fprintf('文件名: four_activation_functions.png 和 four_activation_functions.fig\n');
