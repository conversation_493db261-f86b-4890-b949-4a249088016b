% 激活函数可视化
% 生成Sigmoid、<PERSON><PERSON>、<PERSON><PERSON> ReLU、ReLU的图像

% 创建输入范围
x = -10:0.1:10;

% 创建图形窗口
figure('Position', [100, 100, 1200, 300]);

% (a) Sigmoid函数
subplot(1, 4, 1);
y_sigmoid = 1 ./ (1 + exp(-x));
plot(x, y_sigmoid, 'r-', 'LineWidth', 2);
grid on;
xlim([-10, 10]);
ylim([0, 1]);
xlabel('x');
ylabel('f(x)');
title('(a) Sigmoid');
set(gca, 'XTick', [-10, 0, 10]);
set(gca, 'YTick', [0, 0.5, 1]);

% (b) Tanh函数
subplot(1, 4, 2);
y_tanh = tanh(x);
plot(x, y_tanh, 'c-', 'LineWidth', 2);
grid on;
xlim([-10, 10]);
ylim([-1, 1]);
xlabel('x');
ylabel('f(x)');
title('(b) Tanh');
set(gca, 'XTick', [-10, 0, 10]);
set(gca, 'YTick', [-1, 0, 1]);

% (c) ReLU函数
subplot(1, 4, 3);
y_relu = max(0, x);
plot(x, y_relu, 'b-', 'LineWidth', 2);
grid on;
xlim([-10, 10]);
ylim([0, 10]);
xlabel('x');
ylabel('f(x)');
title('(c) ReLU');
set(gca, 'XTick', [-10, 0, 10]);
set(gca, 'YTick', [0, 5, 10]);

% (d) Leaky ReLU函数 (这里用GELU替代，如截图所示)
subplot(1, 4, 4);
% GELU函数: f(x) = 0.5 * x * (1 + tanh(sqrt(2/π) * (x + 0.044715 * x^3)))
y_gelu = 0.5 * x .* (1 + tanh(sqrt(2/pi) * (x + 0.044715 * x.^3)));
plot(x, y_gelu, 'm-', 'LineWidth', 2);
grid on;
xlim([-10, 10]);
ylim([0, 10]);
xlabel('x');
ylabel('f(x)');
title('(d) GELU');
set(gca, 'XTick', [-10, 0, 10]);
set(gca, 'YTick', [0, 5, 10]);

% 调整子图间距
sgtitle('激活函数对比', 'FontSize', 14, 'FontWeight', 'bold');

% 保存图像
saveas(gcf, 'activation_functions.png', 'png');
saveas(gcf, 'activation_functions.fig', 'fig');

fprintf('激活函数图像已生成并保存为 activation_functions.png 和 activation_functions.fig\n');
