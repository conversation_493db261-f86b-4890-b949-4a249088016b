<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <style>
      .input-matrix { fill: #f0f8ff; stroke: #333; stroke-width: 1.5; }
      .kernel-matrix { fill: #ffe4e1; stroke: #333; stroke-width: 1.5; }
      .output-matrix { fill: #f0fff0; stroke: #333; stroke-width: 1.5; }
      .text-label { font-family: 'Times New Roman', serif; font-size: 14px; fill: #333; text-anchor: middle; }
      .text-small { font-family: 'Times New Roman', serif; font-size: 12px; fill: #333; text-anchor: middle; }
      .text-formula { font-family: 'Times New Roman', serif; font-size: 11px; fill: #333; text-anchor: middle; }
      .operation-symbol { font-family: 'Times New Roman', serif; font-size: 24px; fill: #d2691e; font-weight: bold; text-anchor: middle; }
      .equals-symbol { font-family: 'Times New Roman', serif; font-size: 20px; fill: #333; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 标题 -->
  <text x="400" y="25" class="text-label" style="font-size: 16px; font-weight: bold;">二维卷积运算示意图 (步长=2, 填充=0)</text>
  
  <!-- 输入特征图 (4x4) -->
  <g id="input-matrix">
    <!-- 矩阵框架 -->
    <rect x="50" y="60" width="160" height="160" class="input-matrix"/>

    <!-- 步长为2的卷积窗口高亮 -->
    <!-- 第一个卷积窗口 (1,1) -->
    <rect x="50" y="60" width="80" height="80" fill="rgba(255,0,0,0.2)" stroke="red" stroke-width="2" stroke-dasharray="5,5"/>
    <!-- 第二个卷积窗口 (1,3) -->
    <rect x="130" y="60" width="80" height="80" fill="rgba(0,255,0,0.2)" stroke="green" stroke-width="2" stroke-dasharray="5,5"/>
    <!-- 第三个卷积窗口 (3,1) -->
    <rect x="50" y="140" width="80" height="80" fill="rgba(0,0,255,0.2)" stroke="blue" stroke-width="2" stroke-dasharray="5,5"/>
    <!-- 第四个卷积窗口 (3,3) -->
    <rect x="130" y="140" width="80" height="80" fill="rgba(255,165,0,0.2)" stroke="orange" stroke-width="2" stroke-dasharray="5,5"/>

    <!-- 网格线 -->
    <line x1="90" y1="60" x2="90" y2="220" stroke="#333" stroke-width="1"/>
    <line x1="130" y1="60" x2="130" y2="220" stroke="#333" stroke-width="1"/>
    <line x1="170" y1="60" x2="170" y2="220" stroke="#333" stroke-width="1"/>
    <line x1="50" y1="100" x2="210" y2="100" stroke="#333" stroke-width="1"/>
    <line x1="50" y1="140" x2="210" y2="140" stroke="#333" stroke-width="1"/>
    <line x1="50" y1="180" x2="210" y2="180" stroke="#333" stroke-width="1"/>
    
    <!-- 元素标签 -->
    <text x="70" y="85" class="text-small">x₁₁</text>
    <text x="110" y="85" class="text-small">x₁₂</text>
    <text x="150" y="85" class="text-small">x₁₃</text>
    <text x="190" y="85" class="text-small">x₁₄</text>
    
    <text x="70" y="125" class="text-small">x₂₁</text>
    <text x="110" y="125" class="text-small">x₂₂</text>
    <text x="150" y="125" class="text-small">x₂₃</text>
    <text x="190" y="125" class="text-small">x₂₄</text>
    
    <text x="70" y="165" class="text-small">x₃₁</text>
    <text x="110" y="165" class="text-small">x₃₂</text>
    <text x="150" y="165" class="text-small">x₃₃</text>
    <text x="190" y="165" class="text-small">x₃₄</text>
    
    <text x="70" y="205" class="text-small">x₄₁</text>
    <text x="110" y="205" class="text-small">x₄₂</text>
    <text x="150" y="205" class="text-small">x₄₃</text>
    <text x="190" y="205" class="text-small">x₄₄</text>
    
    <!-- 标签 -->
    <text x="130" y="245" class="text-label">输入特征图 (4×4)</text>
  </g>
  
  <!-- 卷积操作符 -->
  <text x="250" y="150" class="operation-symbol">⊗</text>
  
  <!-- 卷积核 (2x2) -->
  <g id="kernel-matrix">
    <!-- 矩阵框架 -->
    <rect x="290" y="110" width="80" height="80" class="kernel-matrix"/>
    
    <!-- 网格线 -->
    <line x1="330" y1="110" x2="330" y2="190" stroke="#333" stroke-width="1"/>
    <line x1="290" y1="150" x2="370" y2="150" stroke="#333" stroke-width="1"/>
    
    <!-- 元素标签 -->
    <text x="310" y="135" class="text-small">w₁₁</text>
    <text x="350" y="135" class="text-small">w₁₂</text>
    <text x="310" y="175" class="text-small">w₂₁</text>
    <text x="350" y="175" class="text-small">w₂₂</text>
    
    <!-- 标签 -->
    <text x="330" y="210" class="text-label">卷积核 (2×2)</text>
  </g>
  
  <!-- 等号 -->
  <text x="410" y="150" class="equals-symbol">=</text>
  
  <!-- 输出特征图 (2x2) -->
  <g id="output-matrix">
    <!-- 矩阵框架 -->
    <rect x="450" y="110" width="240" height="80" class="output-matrix"/>

    <!-- 网格线 -->
    <line x1="570" y1="110" x2="570" y2="190" stroke="#333" stroke-width="1"/>
    <line x1="450" y1="150" x2="690" y2="150" stroke="#333" stroke-width="1"/>

    <!-- 输出元素公式 -->
    <text x="510" y="135" class="text-formula">y₁₁ = x₁₁w₁₁ + x₁₃w₁₂</text>
    <text x="510" y="147" class="text-formula">    + x₃₁w₂₁ + x₃₃w₂₂</text>

    <text x="630" y="135" class="text-formula">y₁₂ = x₁₃w₁₁ + x₁₄w₁₂</text>
    <text x="630" y="147" class="text-formula">    + x₃₃w₂₁ + x₃₄w₂₂</text>

    <text x="510" y="175" class="text-formula">y₂₁ = x₃₁w₁₁ + x₃₃w₁₂</text>
    <text x="510" y="187" class="text-formula">    + x₄₁w₂₁ + x₄₃w₂₂</text>

    <text x="630" y="175" class="text-formula">y₂₂ = x₃₃w₁₁ + x₃₄w₁₂</text>
    <text x="630" y="187" class="text-formula">    + x₄₃w₂₁ + x₄₄w₂₂</text>

    <!-- 标签 -->
    <text x="570" y="210" class="text-label">输出特征图 (2×2)</text>
  </g>
  
  <!-- 参数说明 -->
  <g id="parameters">
    <text x="50" y="280" class="text-small" style="text-anchor: start;">步长 (Stride): 2</text>
    <text x="50" y="300" class="text-small" style="text-anchor: start;">填充 (Padding): 0</text>
    <text x="50" y="320" class="text-small" style="text-anchor: start;">输出尺寸: (N-K+2P)/S + 1 = (4-2+0)/2 + 1 = 2</text>
  </g>
  
  <!-- 图例说明 -->
  <g id="legend">
    <rect x="450" y="280" width="15" height="15" class="input-matrix"/>
    <text x="475" y="292" class="text-small" style="text-anchor: start;">输入特征图</text>
    
    <rect x="450" y="305" width="15" height="15" class="kernel-matrix"/>
    <text x="475" y="317" class="text-small" style="text-anchor: start;">卷积核</text>
    
    <rect x="450" y="330" width="15" height="15" class="output-matrix"/>
    <text x="475" y="342" class="text-small" style="text-anchor: start;">输出特征图</text>
  </g>
  
  <!-- 步长说明 -->
  <g id="stride-explanation">
    <text x="50" y="350" class="text-small" style="text-anchor: start; fill: red;">红色区域: 第1次卷积 (位置1,1) → y₁₁</text>
    <text x="50" y="365" class="text-small" style="text-anchor: start; fill: green;">绿色区域: 第2次卷积 (位置1,3) → y₁₂</text>
    <text x="400" y="350" class="text-small" style="text-anchor: start; fill: blue;">蓝色区域: 第3次卷积 (位置3,1) → y₂₁</text>
    <text x="400" y="365" class="text-small" style="text-anchor: start; fill: orange;">橙色区域: 第4次卷积 (位置3,3) → y₂₂</text>
  </g>

  <!-- 数学公式 -->
  <text x="400" y="390" class="text-label" style="font-style: italic;">
    y[i,j] = Σₘ Σₙ x[i×S+m, j×S+n] × w[m,n]  (S=步长)
  </text>
</svg>
