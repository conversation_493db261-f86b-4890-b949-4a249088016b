% 激活函数可视化
% 生成Sigmoid、<PERSON><PERSON>、<PERSON>ky ReLU、ReLU的图像

% 创建输入范围
x = -10:0.1:10;

% 创建图形窗口
figure('Position', [100, 100, 1200, 300]);

% (a) Sigmoid函数
subplot(1, 4, 1);
y_sigmoid = 1 ./ (1 + exp(-x));
plot(x, y_sigmoid, 'r-', 'LineWidth', 2);
grid on;
xlim([-10, 10]);
ylim([-10, 10]);
xlabel('x');
ylabel('f(x)');
title('(a) Sigmoid');
set(gca, 'XTick', [-10, 0, 10]);
set(gca, 'YTick', [-10, -5, 0, 5, 10]);

% (b) Tanh函数
subplot(1, 4, 2);
y_tanh = tanh(x);
plot(x, y_tanh, 'c-', 'LineWidth', 2);
grid on;
xlim([-10, 10]);
ylim([-10, 10]);
xlabel('x');
ylabel('f(x)');
title('(b) Tanh');
set(gca, 'XTick', [-10, 0, 10]);
set(gca, 'YTick', [-10, -5, 0, 5, 10]);

% (c) Leaky ReLU函数
subplot(1, 4, 3);
alpha = 0.01; % Leaky ReLU的负斜率参数
y_leaky_relu = max(alpha * x, x);
plot(x, y_leaky_relu, 'b-', 'LineWidth', 2);
grid on;
xlim([-10, 10]);
ylim([-10, 10]);
xlabel('x');
ylabel('f(x)');
title('(c) Leaky ReLU');
set(gca, 'XTick', [-10, 0, 10]);
set(gca, 'YTick', [-10, -5, 0, 5, 10]);

% (d) ReLU函数
subplot(1, 4, 4);
y_relu = max(0, x);
plot(x, y_relu, 'm-', 'LineWidth', 2);
grid on;
xlim([-10, 10]);
ylim([-10, 10]);
xlabel('x');
ylabel('f(x)');
title('(d) ReLU');
set(gca, 'XTick', [-10, 0, 10]);
set(gca, 'YTick', [-10, -5, 0, 5, 10]);

% 调整子图间距
sgtitle('激活函数对比', 'FontSize', 14, 'FontWeight', 'bold');

% 保存图像
saveas(gcf, 'activation_functions.png', 'png');
saveas(gcf, 'activation_functions.fig', 'fig');

fprintf('激活函数图像已生成并保存为 activation_functions.png 和 activation_functions.fig\n');
