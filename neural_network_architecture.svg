<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Define gradients for better visual appeal -->
    <linearGradient id="inputGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFE4B5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DEB887;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="filterGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E6E6FA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D8BFD8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="mapGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F0F8FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B0E0E6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="400" fill="#FAFAFA" stroke="none"/>
  
  <!-- Input Layer - 3 Channel Input -->
  <g id="input-layer">
    <!-- Input channels (stacked rectangles) -->
    <rect x="20" y="120" width="80" height="60" fill="url(#inputGradient)" stroke="#8B4513" stroke-width="1"/>
    <rect x="30" y="110" width="80" height="60" fill="url(#inputGradient)" stroke="#8B4513" stroke-width="1"/>
    <rect x="40" y="100" width="80" height="60" fill="url(#inputGradient)" stroke="#8B4513" stroke-width="1"/>
    
    <!-- Label -->
    <text x="80" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold">3 Channel Input</text>
  </g>
  
  <!-- First Convolution Layer -->
  <g id="conv1">
    <!-- Filters * 3 -->
    <text x="200" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Filters * 3</text>
    
    <!-- Filter grid -->
    <g id="filter-grid">
      <rect x="170" y="80" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
      <rect x="185" y="80" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
      <rect x="200" y="80" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
      <rect x="170" y="95" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
      <rect x="185" y="95" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
      <rect x="200" y="95" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
      <rect x="170" y="110" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
      <rect x="185" y="110" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
      <rect x="200" y="110" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
    </g>
    
    <!-- Additional filter representations -->
    <rect x="180" y="140" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
    <rect x="190" y="150" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
  </g>
  
  <!-- First Feature Maps -->
  <g id="maps1">
    <text x="320" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Maps * 3</text>
    
    <!-- Feature maps (stacked) -->
    <rect x="280" y="120" width="80" height="60" fill="url(#mapGradient)" stroke="#4682B4" stroke-width="1"/>
    <rect x="290" y="110" width="80" height="60" fill="url(#mapGradient)" stroke="#4682B4" stroke-width="1"/>
    <rect x="300" y="100" width="80" height="60" fill="url(#mapGradient)" stroke="#4682B4" stroke-width="1"/>
  </g>
  
  <!-- Second Convolution Layer -->
  <g id="conv2">
    <text x="480" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Filters * 4</text>
    
    <!-- 3D filter representation -->
    <g id="filter-3d">
      <!-- Multiple small cubes to represent 3D filters -->
      <rect x="450" y="100" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
      <rect x="462" y="100" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
      <rect x="450" y="112" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
      <rect x="462" y="112" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
      
      <!-- Depth representation -->
      <rect x="455" y="95" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
      <rect x="467" y="95" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
      <rect x="455" y="107" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
      <rect x="467" y="107" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
      
      <rect x="460" y="90" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
      <rect x="472" y="90" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
      <rect x="460" y="102" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
      <rect x="472" y="102" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
    </g>
  </g>
  
  <!-- Second Feature Maps -->
  <g id="maps2">
    <text x="650" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Maps * 4</text>
    
    <!-- Multiple feature maps -->
    <rect x="600" y="130" width="70" height="50" fill="url(#mapGradient)" stroke="#4682B4" stroke-width="1"/>
    <rect x="610" y="120" width="70" height="50" fill="url(#mapGradient)" stroke="#4682B4" stroke-width="1"/>
    <rect x="620" y="110" width="70" height="50" fill="url(#mapGradient)" stroke="#4682B4" stroke-width="1"/>
    <rect x="630" y="100" width="70" height="50" fill="url(#mapGradient)" stroke="#4682B4" stroke-width="1"/>
  </g>
  
  <!-- Arrows and connections -->
  <g id="connections">
    <!-- Input to Conv1 -->
    <path d="M 130 130 Q 150 130 170 110" fill="none" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- Conv1 to Maps1 -->
    <path d="M 220 110 Q 250 110 280 130" fill="none" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- Maps1 to Conv2 -->
    <path d="M 380 130 Q 415 130 450 110" fill="none" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- Conv2 to Maps2 -->
    <path d="M 485 110 Q 540 110 600 130" fill="none" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
  </defs>
  
  <!-- Section labels with brackets -->
  <g id="section-labels">
    <!-- Convolution section bracket -->
    <path d="M 20 250 Q 20 260 30 260 L 380 260 Q 390 260 390 250" fill="none" stroke="#333" stroke-width="2"/>
    <text x="205" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold">逐通道卷积</text>
    
    <!-- Pointwise convolution section bracket -->
    <path d="M 410 250 Q 410 260 420 260 L 750 260 Q 760 260 760 250" fill="none" stroke="#333" stroke-width="2"/>
    <text x="585" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold">逐点卷积</text>
  </g>
  
  <!-- Additional visual elements for depth -->
  <g id="depth-lines">
    <!-- Lines to show 3D effect -->
    <line x1="120" y1="100" x2="130" y2="90" stroke="#8B4513" stroke-width="1"/>
    <line x1="120" y1="160" x2="130" y2="150" stroke="#8B4513" stroke-width="1"/>
    <line x1="40" y1="100" x2="50" y2="90" stroke="#8B4513" stroke-width="1"/>
    
    <line x1="380" y1="100" x2="390" y2="90" stroke="#4682B4" stroke-width="1"/>
    <line x1="380" y1="160" x2="390" y2="150" stroke="#4682B4" stroke-width="1"/>
    
    <line x1="700" y1="100" x2="710" y2="90" stroke="#4682B4" stroke-width="1"/>
    <line x1="700" y1="150" x2="710" y2="140" stroke="#4682B4" stroke-width="1"/>
  </g>
</svg>
