<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="350" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Arrow marker -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>

  <!-- Background -->
  <rect width="900" height="350" fill="white" stroke="none"/>

  <!-- Labels at top -->
  <text x="80" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold">3 Channel Input</text>
  <text x="240" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Filters * 3</text>
  <text x="400" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Maps * 3</text>
  <text x="580" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Filters * 4</text>
  <text x="750" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Maps * 4</text>

  <!-- 3 Channel Input - stacked colored rectangles -->
  <g id="input">
    <!-- Bottom layer (yellow/beige) -->
    <rect x="20" y="120" width="80" height="60" fill="#F5DEB3" stroke="#8B4513" stroke-width="1.5"/>
    <!-- Middle layer (light blue) -->
    <rect x="30" y="100" width="80" height="60" fill="#ADD8E6" stroke="#4682B4" stroke-width="1.5"/>
    <!-- Top layer (light green) -->
    <rect x="40" y="80" width="80" height="60" fill="#90EE90" stroke="#228B22" stroke-width="1.5"/>
  </g>

  <!-- Curved arrow from input to filters -->
  <path d="M 130 120 Q 160 90 190 100" fill="none" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- Filters * 3 - 3x3 grid pattern with cylindrical filters -->
  <g id="filters3">
    <!-- Main 3x3 grid -->
    <g id="grid">
      <rect x="200" y="70" width="18" height="18" fill="white" stroke="black" stroke-width="1"/>
      <rect x="218" y="70" width="18" height="18" fill="white" stroke="black" stroke-width="1"/>
      <rect x="236" y="70" width="18" height="18" fill="white" stroke="black" stroke-width="1"/>
      <rect x="200" y="88" width="18" height="18" fill="white" stroke="black" stroke-width="1"/>
      <rect x="218" y="88" width="18" height="18" fill="white" stroke="black" stroke-width="1"/>
      <rect x="236" y="88" width="18" height="18" fill="white" stroke="black" stroke-width="1"/>
      <rect x="200" y="106" width="18" height="18" fill="white" stroke="black" stroke-width="1"/>
      <rect x="218" y="106" width="18" height="18" fill="white" stroke="black" stroke-width="1"/>
      <rect x="236" y="106" width="18" height="18" fill="white" stroke="black" stroke-width="1"/>
    </g>

    <!-- Cylindrical filters representation -->
    <ellipse cx="225" cy="140" rx="15" ry="8" fill="#E6E6FA" stroke="black" stroke-width="1"/>
    <rect x="210" y="140" width="30" height="25" fill="#E6E6FA" stroke="black" stroke-width="1"/>
    <ellipse cx="225" cy="165" rx="15" ry="8" fill="#E6E6FA" stroke="black" stroke-width="1"/>

    <!-- Additional smaller filters -->
    <ellipse cx="260" cy="150" rx="10" ry="5" fill="#E6E6FA" stroke="black" stroke-width="1"/>
    <rect x="250" y="150" width="20" height="15" fill="#E6E6FA" stroke="black" stroke-width="1"/>
    <ellipse cx="260" cy="165" rx="10" ry="5" fill="#E6E6FA" stroke="black" stroke-width="1"/>
  </g>

  <!-- Curved arrows from filters to maps -->
  <path d="M 280 100 Q 320 80 350 100" fill="none" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 280 120 Q 320 100 350 120" fill="none" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 280 140 Q 320 120 350 140" fill="none" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- Maps * 3 - stacked feature maps -->
  <g id="maps3">
    <rect x="350" y="130" width="90" height="70" fill="#F0F8FF" stroke="#4682B4" stroke-width="1.5"/>
    <rect x="360" y="110" width="90" height="70" fill="#F0F8FF" stroke="#4682B4" stroke-width="1.5"/>
    <rect x="370" y="90" width="90" height="70" fill="#F0F8FF" stroke="#4682B4" stroke-width="1.5"/>
  </g>

  <!-- Curved arrow from maps3 to filters4 -->
  <path d="M 470 130 Q 510 110 540 130" fill="none" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- Filters * 4 - 1x1 convolution represented as small cubes -->
  <g id="filters4">
    <!-- Small 1x1 filter cubes arranged in depth -->
    <g id="cube1">
      <rect x="540" y="120" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
      <polygon points="540,120 545,115 560,115 555,120" fill="#E0E0E0" stroke="black" stroke-width="1"/>
      <polygon points="555,120 560,115 560,130 555,135" fill="#C0C0C0" stroke="black" stroke-width="1"/>
    </g>
    <g id="cube2">
      <rect x="550" y="110" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
      <polygon points="550,110 555,105 570,105 565,110" fill="#E0E0E0" stroke="black" stroke-width="1"/>
      <polygon points="565,110 570,105 570,120 565,125" fill="#C0C0C0" stroke="black" stroke-width="1"/>
    </g>
    <g id="cube3">
      <rect x="560" y="100" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
      <polygon points="560,100 565,95 580,95 575,100" fill="#E0E0E0" stroke="black" stroke-width="1"/>
      <polygon points="575,100 580,95 580,110 575,115" fill="#C0C0C0" stroke="black" stroke-width="1"/>
    </g>
    <g id="cube4">
      <rect x="570" y="90" width="15" height="15" fill="white" stroke="black" stroke-width="1"/>
      <polygon points="570,90 575,85 590,85 585,90" fill="#E0E0E0" stroke="black" stroke-width="1"/>
      <polygon points="585,90 590,85 590,100 585,105" fill="#C0C0C0" stroke="black" stroke-width="1"/>
    </g>

    <!-- Additional cubes to show depth -->
    <rect x="545" y="140" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
    <rect x="555" y="150" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
    <rect x="565" y="160" width="12" height="12" fill="white" stroke="black" stroke-width="1"/>
  </g>

  <!-- Multiple curved arrows from filters4 to maps4 -->
  <path d="M 590 100 Q 630 90 670 110" fill="none" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 590 120 Q 630 110 670 130" fill="none" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 590 140 Q 630 130 670 150" fill="none" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 590 160 Q 630 150 670 170" fill="none" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- Maps * 4 - multiple stacked feature maps -->
  <g id="maps4">
    <rect x="670" y="160" width="100" height="60" fill="#F5F5F5" stroke="#696969" stroke-width="1.5"/>
    <rect x="680" y="140" width="100" height="60" fill="#F5F5F5" stroke="#696969" stroke-width="1.5"/>
    <rect x="690" y="120" width="100" height="60" fill="#F5F5F5" stroke="#696969" stroke-width="1.5"/>
    <rect x="700" y="100" width="100" height="60" fill="#F5F5F5" stroke="#696969" stroke-width="1.5"/>
  </g>

  <!-- Section dividing brackets -->
  <g id="brackets">
    <!-- Depthwise convolution bracket -->
    <path d="M 10 240 Q 10 250 20 250 L 470 250 Q 480 250 480 240" fill="none" stroke="#333" stroke-width="2"/>
    <text x="245" y="270" text-anchor="middle" font-family="SimSun, serif" font-size="16" font-weight="bold">逐通道卷积</text>

    <!-- Pointwise convolution bracket -->
    <path d="M 500 240 Q 500 250 510 250 L 850 250 Q 860 250 860 240" fill="none" stroke="#333" stroke-width="2"/>
    <text x="680" y="270" text-anchor="middle" font-family="SimSun, serif" font-size="16" font-weight="bold">逐点卷积</text>
  </g>

</svg>
